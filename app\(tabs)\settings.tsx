import React from 'react';
import { ScrollView, StyleSheet, View, Pressable, Alert, Linking } from 'react-native';

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { useSubscription } from '@/hooks/useSubscription';
import { ThemeMode, Language } from '@/lib/types';
import { Crown, Settings as SettingsIcon, ChevronRight, Plus } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useColorScheme as useNativewindColorScheme } from 'nativewind';
import { useColorScheme as useNativeColorScheme } from 'react-native';
import { setAndroidNavigationBar } from '@/lib/android-navigation-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SubscriptionDebug } from '@/components/SubscriptionDebug';

export default function SettingsScreen() {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const iconColor = colorScheme === 'dark' ? '#ffffff' : '#000000';

  const { settings, updateSettings, configs, clearAllData } = useAppStore();
  const {
    isPro,
    isLoading,
    isConnected,
    purchasePro,
    restorePurchases,
    error,
    subscriptions,
    currentPurchase
  } = useSubscription();
  const { setColorScheme: setNativewindColorScheme } = useNativewindColorScheme();
  const systemColorScheme = useNativeColorScheme();
  const insets = useSafeAreaInsets();

  // 主题选项
  const themeOptions = [
    { label: t('settings.themes.system'), value: 'system' as ThemeMode },
    { label: t('settings.themes.light'), value: 'light' as ThemeMode },
    { label: t('settings.themes.dark'), value: 'dark' as ThemeMode },
  ];

  // 语言选项
  const languageOptions = [
    { label: t('settings.languages.en'), value: 'en' as Language },
    { label: t('settings.languages.zh-CN'), value: 'zh-CN' as Language },
    { label: t('settings.languages.zh-TW'), value: 'zh-TW' as Language },
    { label: t('settings.languages.fa'), value: 'fa' as Language },
  ];

  // 处理主题选择
  const handleThemePress = () => {
    Alert.alert(
      t('settings.theme'),
      '',
      [
        ...themeOptions.map(option => ({
          text: option.label,
          onPress: () => {
            // 更新应用设置
            updateSettings({ theme: option.value });

            // 立即同步更新NativeWind的colorScheme和Android导航栏
            let targetTheme: 'light' | 'dark';
            if (option.value === 'system') {
              // 当选择跟随系统时，使用当前系统主题
              targetTheme = systemColorScheme || 'light';
            } else {
              // 当手动选择主题时，直接使用选择的主题
              targetTheme = option.value;
            }
            setNativewindColorScheme(targetTheme);
            setAndroidNavigationBar(targetTheme);
          },
        })),
        { text: t('common.cancel'), style: 'cancel' }
      ]
    );
  };

  // 处理语言选择
  const handleLanguagePress = () => {
    Alert.alert(
      t('settings.language'),
      '',
      [
        ...languageOptions.map(option => ({
          text: option.label,
          onPress: () => updateSettings({ language: option.value }),
        })),
        { text: t('common.cancel'), style: 'cancel' }
      ]
    );
  };

  // 处理购买Pro
  const handlePurchasePro = async () => {
    if (!isConnected) {
      Alert.alert(t('common.error'), 'Store connection not available');
      return;
    }

    try {
      await purchasePro();
      // 成功消息会在hook的onPurchaseSuccess回调中处理
    } catch (error) {
      // 错误已经在hook中设置，这里显示通用错误
      const errorMessage = error instanceof Error ? error.message : t('settings.purchaseError');
      Alert.alert(t('common.error'), errorMessage);
    }
  };

  // 处理恢复购买
  const handleRestorePurchases = async () => {
    if (!isConnected) {
      Alert.alert(t('common.error'), 'Store connection not available');
      return;
    }

    try {
      await restorePurchases();
      Alert.alert(t('common.success'), t('settings.restoreSuccess'));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : t('settings.restoreError');
      Alert.alert(t('common.error'), errorMessage);
    }
  };

  // 加入群组
  const handleJoinGroup = () => {
    const groupUrl = 'https://t.me/rayboxui';
    Linking.openURL(groupUrl).catch(() => {
      Alert.alert(t('common.error'), t('settings.openLinkError'));
    });
  };

  // 隐私政策与清除数据
  const handlePrivacyPolicyPress = () => {
    const privacyUrl = 'https://rayboxui.pages.dev/zh/privacy/';
    Linking.openURL(privacyUrl).catch(() => {
      Alert.alert(t('common.error'), t('settings.openLinkError'));
    });
  };

  const handleClearDataPress = () => {
    Alert.alert(
      t('common.confirm'),
      t('settings.clearDataConfirmMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.ok'),
          style: 'destructive',
          onPress: async () => {
            try {
              await clearAllData();
              Alert.alert(t('common.success'), t('settings.clearDataSuccess'));
            } catch (e) {
              Alert.alert(t('common.error'), t('settings.clearDataError'));
            }
          }
        }
      ]
    );
  };

  // 获取当前主题显示文本
  const getCurrentThemeLabel = () => {
    return themeOptions.find(option => option.value === settings.theme)?.label || t('settings.themes.system');
  };

  // 获取当前语言显示文本
  const getCurrentLanguageLabel = () => {
    return languageOptions.find(option => option.value === settings.language)?.label || t('settings.languages.en');
  };

  // 检查是否达到配置限制
  const isConfigLimitReached = !isPro && configs.length >= 1;

  return (
    <View style={[styles.container, { backgroundColor,paddingTop: insets.top }]}>
      {/* 顶部标题 */}
      <View style={styles.header}>
        <Text className="text-2xl font-bold">{t('settings.title')}</Text>
        <View style={styles.headerButtons}>
          <Button
            variant="ghost"
            size="icon"
          >
            <Plus strokeWidth={3} size={25} opacity={0} color={iconColor} />
          </Button>
        </View>
      </View>

      {/* 设置内容 */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Pro订阅部分 */}
        <View style={[styles.section, { borderColor }]}>
          <View style={styles.sectionHeader}>
            <Crown size={20} color={isPro ? '#FFD700' : '#8E8E93'} />
            <Text className="text-xl font-semibold ml-2">{t('settings.proPlan')}</Text>
            {/* 连接状态指示器 */}
            <View style={styles.statusIndicator}>
              <Badge variant={isConnected ? "default" : "destructive"} className="ml-2">
                <Text className="text-xs">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </Text>
              </Badge>
            </View>
          </View>

          <View style={styles.sectionContent}>
            <Text className="text-base text-muted-foreground mb-4">
              {isPro
                ? t('settings.proActive')
                : t('settings.proDescription')
              }
            </Text>

            {/* 错误信息显示 */}
            {error && (
              <View style={styles.errorContainer}>
                <Text className="text-sm text-destructive">
                  {error}
                </Text>
              </View>
            )}

            {/* 当前购买状态 */}
            {currentPurchase && (
              <View style={styles.purchaseContainer}>
                <Text className="text-sm text-green-600 dark:text-green-400">
                  Processing purchase: {currentPurchase.id}
                </Text>
              </View>
            )}

            {/* 订阅产品信息 */}
            {subscriptions.length > 0 && !isPro && (
              <View style={styles.subscriptionInfo}>
                {subscriptions.map((subscription) => (
                  <View key={subscription.id} style={styles.subscriptionItem}>
                    <Text className="text-base font-medium">{subscription.title}</Text>
                    <Text className="text-lg font-bold text-blue-600">
                      {subscription.displayPrice}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            {!isPro && (
              <View style={styles.configLimitInfo}>
                <Text className="text-base text-muted-foreground">
                  {t('settings.configLimit')}: {configs.length}/1
                </Text>
                {isConfigLimitReached && (
                  <Text className="text-sm text-destructive mt-1">
                    {t('settings.configLimitReached')}
                  </Text>
                )}
              </View>
            )}

            <View style={styles.buttonRow}>
              {!isPro && (
                <Button
                  onPress={handlePurchasePro}
                  disabled={isLoading || !isConnected}
                  className="flex-1"
                >
                  <Text className="text-base">
                    {isLoading ? t('common.loading') : t('settings.subscribeToPro')}
                  </Text>
                </Button>
              )}

              <Button
                variant="outline"
                onPress={handleRestorePurchases}
                disabled={isLoading || !isConnected}
                className={isPro ? "flex-1" : "ml-2"}
              >
                <Text className="text-base">{t('settings.restorePurchases')}</Text>
              </Button>
            </View>
          </View>
        </View>

        <Separator className="my-6" />

        {/* 应用设置 */}
        <View style={[styles.section, { borderColor }]}>
          <View style={styles.sectionHeader}>
            <SettingsIcon size={20} color="#8E8E93" />
            <Text className="text-xl font-semibold ml-2">{t('settings.title')}</Text>
          </View>

          <View style={styles.sectionContent}>
            {/* 主题设置 */}
            <Pressable
              style={styles.settingItem}
              onPress={handleThemePress}
            >
              <Text className="text-lg">{t('settings.theme')}</Text>
              <View style={styles.settingValue}>
                <Text className="text-base text-muted-foreground mr-2">
                  {getCurrentThemeLabel()}
                </Text>
                <ChevronRight size={16} color="#8E8E93" />
              </View>
            </Pressable>

            <Separator className="my-3" />

            {/* 语言设置 */}
            <Pressable
              style={styles.settingItem}
              onPress={handleLanguagePress}
            >
              <Text className="text-lg">{t('settings.language')}</Text>
              <View style={styles.settingValue}>
                <Text className="text-base text-muted-foreground mr-2">
                  {getCurrentLanguageLabel()}
                </Text>
                <ChevronRight size={16} color="#8E8E93" />
              </View>
            </Pressable>

            <Separator className="my-3" />

            {/* 群组链接 */}
            <Pressable
              style={styles.settingItem}
              onPress={handleJoinGroup}
            >
              <Text className="text-lg">{t('settings.messengerGroup')}</Text>
              <View style={styles.settingValue}>
                <ChevronRight size={16} color="#8E8E93" />
              </View>
            </Pressable>

            <Separator className="my-3" />

            {/* 隐私政策 */}
            <Pressable
              style={styles.settingItem}
              onPress={handlePrivacyPolicyPress}
            >
              <Text className="text-lg">{t('settings.privacyPolicy')}</Text>
              <View style={styles.settingValue}>
                <ChevronRight size={16} color="#8E8E93" />
              </View>
            </Pressable>

            <Separator className="my-3" />

            {/* 清除数据 */}
            <Pressable
              style={styles.settingItem}
              onPress={handleClearDataPress}
            >
              <Text className="text-lg text-destructive">{t('settings.clearData')}</Text>
              <View style={styles.settingValue}>
                <ChevronRight size={16} color="#8E8E93" />
              </View>
            </Pressable>
          </View>
        </View>

        {/* 调试信息 - 仅开发环境 */}
        {__DEV__ && <SubscriptionDebug />}

        {/* 底部间距 */}
        <View style={{ height: 40 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionContent: {
    // 移除gap，使用Separator分隔
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  configLimitInfo: {
    marginBottom: 12,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
  },
  statusIndicator: {
    marginLeft: 'auto',
  },
  errorContainer: {
    backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  purchaseContainer: {
    backgroundColor: '#F0FDF4',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  subscriptionInfo: {
    marginBottom: 12,
  },
  subscriptionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
    marginBottom: 8,
  },
});
