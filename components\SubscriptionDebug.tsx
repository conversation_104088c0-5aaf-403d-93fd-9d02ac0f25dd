import React from 'react';
import { View, Text } from 'react-native';
import { useSubscription } from '@/hooks/useSubscription';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

/**
 * 订阅调试组件
 * 用于开发环境下调试和测试订阅功能
 */
export function SubscriptionDebug() {
  const {
    isPro,
    isLoading,
    isConnected,
    error,
    products,
    subscriptions,
    currentPurchase,
  } = useSubscription();

  // 只在开发环境显示
  if (!__DEV__) {
    return null;
  }

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Subscription Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <View className="flex-row justify-between">
          <Text className="text-sm">Connected:</Text>
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "Yes" : "No"}
          </Badge>
        </View>
        
        <View className="flex-row justify-between">
          <Text className="text-sm">Pro Status:</Text>
          <Badge variant={isPro ? "default" : "secondary"}>
            {isPro ? "Active" : "Inactive"}
          </Badge>
        </View>
        
        <View className="flex-row justify-between">
          <Text className="text-sm">Loading:</Text>
          <Text className="text-sm">{isLoading ? "Yes" : "No"}</Text>
        </View>
        
        <View className="flex-row justify-between">
          <Text className="text-sm">Products:</Text>
          <Text className="text-sm">{products.length}</Text>
        </View>
        
        <View className="flex-row justify-between">
          <Text className="text-sm">Subscriptions:</Text>
          <Text className="text-sm">{subscriptions.length}</Text>
        </View>
        
        {error && (
          <View className="p-2 bg-red-50 dark:bg-red-900/20 rounded">
            <Text className="text-red-600 dark:text-red-400 text-xs">
              Error: {error}
            </Text>
          </View>
        )}
        
        {currentPurchase && (
          <View className="p-2 bg-green-50 dark:bg-green-900/20 rounded">
            <Text className="text-green-600 dark:text-green-400 text-xs">
              Current Purchase: {currentPurchase.id}
            </Text>
          </View>
        )}
        
        {subscriptions.length > 0 && (
          <View className="mt-2">
            <Text className="text-sm font-medium mb-1">Available Subscriptions:</Text>
            {subscriptions.map((sub) => (
              <View key={sub.id} className="p-2 bg-gray-50 dark:bg-gray-800 rounded mb-1">
                <Text className="text-xs">{sub.title}</Text>
                <Text className="text-xs text-gray-600">{sub.displayPrice}</Text>
              </View>
            ))}
          </View>
        )}
      </CardContent>
    </Card>
  );
}
