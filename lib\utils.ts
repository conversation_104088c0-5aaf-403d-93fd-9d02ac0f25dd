import { clsx, type ClassValue } from 'clsx';
import * as Crypto from 'expo-crypto';
import { twMerge } from 'tailwind-merge';
import * as forge from 'node-forge'
import { initializeSslPinning, isSslPinningAvailable } from 'react-native-ssl-public-key-pinning';
import { x25519 } from '@noble/curves/ed25519.js';
import { useAppStore } from './store';
import { loginThreeXUI } from '~/panels/3x-ui/utils';
import { loginXUI } from '~/panels/x-ui/utils';
import { fetch } from 'expo/fetch'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 从证书字符串中提取SHA256指纹并转换为base64编码的数组格式
 * @param certString 证书字符串，可以包含多个证书
 * @returns Promise<string[]> 返回SHA256指纹数组，格式为 ["sha256//base64hash", ...]
 */
export async function extractCertFingerprints(certString: string): Promise<string[]> {
  if (!certString || !certString.trim()) {
    return [];
  }

  const fingerprints: string[] = [];

  // 匹配所有证书块（包括证书链）
  const certRegex = /-----BEGIN CERTIFICATE-----[\s\S]*?-----END CERTIFICATE-----/g;
  const certMatches = certString.match(certRegex);

  if (!certMatches) {
    return [];
  }

  for (const certMatch of certMatches) {
    try {
      // 移除证书头尾和换行符，获取纯base64内容
      const certContent = certMatch
        .replace(/-----BEGIN CERTIFICATE-----/g, '')
        .replace(/-----END CERTIFICATE-----/g, '')
        .replace(/\s/g, '');

      // 将base64转换为二进制数据
      const binaryData = atob(certContent);
      const uint8Array = new Uint8Array(binaryData.length);
      for (let i = 0; i < binaryData.length; i++) {
        uint8Array[i] = binaryData.charCodeAt(i);
      }

      // 计算SHA256哈希
      const hashBuffer = await Crypto.digest(Crypto.CryptoDigestAlgorithm.SHA256, uint8Array);

      // 将ArrayBuffer转换为base64
      const hashArray = new Uint8Array(hashBuffer);
      const hashBase64 = btoa(String.fromCharCode(...hashArray));

      // 格式化为标准的证书指纹格式
      fingerprints.push(`sha256//${hashBase64}`);
    } catch (error) {
      console.warn('Failed to extract fingerprint from certificate:', error);
      // 继续处理其他证书，不中断整个过程
    }
  }

  return fingerprints;
}

/**
 * 将十六进制SHA256哈希转换为base64编码
 * @param hexHash 十六进制SHA256哈希值
 * @returns base64编码的哈希值
 */
export function hexToBase64(hexHash: string): string {
  // 移除可能的空格、冒号等分隔符
  const cleanHex = hexHash.replace(/[:\s-]/g, '').toLowerCase();

  // 验证是否为有效的64位十六进制字符串
  if (!/^[0-9a-f]{64}$/i.test(cleanHex)) {
    throw new Error('Invalid SHA256 hash format. Expected 64 hexadecimal characters.');
  }

  // 将十六进制转换为字节数组
  const bytes = new Uint8Array(32);
  for (let i = 0; i < 32; i++) {
    bytes[i] = parseInt(cleanHex.substring(i * 2, i * 2 + 2), 16);
  }

  // 转换为base64
  return btoa(String.fromCharCode(...bytes));
}



/**
 * SSL公钥固定管理器
 * 使用react-native-ssl-public-key-pinning库
 */
export class SSLPinningManager {
  private static pinnedDomains = new Set<string>();

  /**
   * 为指定域名初始化SSL公钥固定
   * @param hostname 域名
   * @param publicKeyHashes 公钥哈希数组（十六进制格式，将在内部转换为base64）
   */
  static async initializePinning(hostname: string, publicKeyHashes: string[]): Promise<boolean> {
    try {

      if (!isSslPinningAvailable()) {
        console.warn('SSL public key pinning not available on this platform');
        return false;
      }

      // 统一将所有哈希转换为base64编码格式
      const processedHashes: string[] = [];
      for (const hash of publicKeyHashes) {
        try {
          const base64Hash = hexToBase64(hash);
          processedHashes.push(base64Hash);
        } catch (error) {
          console.warn(`Failed to process public key hash: ${hash}`, error);
          // 跳过无效的哈希值
        }
      }

      // 确保至少有两个公钥哈希（iOS要求）
      if (processedHashes.length < 2) {
        console.warn('SSL pinning requires at least 2 public key hashes for iOS compatibility');
        // 添加一个备用哈希以满足iOS要求
        processedHashes.push('e4wu8h9eLNeNUg6cVb5gGWM0PsiM9M3i3E32qKOkBwY=');
      }

      await initializeSslPinning({
        [hostname]: {
          includeSubdomains: false,
          publicKeyHashes: processedHashes
        }
      });
      console.log(processedHashes)
      this.pinnedDomains.add(hostname);
      console.log(`SSL pinning initialized for ${hostname} with ${processedHashes.length} hashes`);
      return true;
    } catch (error) {
      console.warn(`Failed to initialize SSL pinning for ${hostname}:`, error);
      return false;
    }
  }

  /**
   * 检查域名是否已固定
   * @param hostname 域名
   */
  static isPinned(hostname: string): boolean {
    return this.pinnedDomains.has(hostname);
  }

  /**
   * 禁用SSL固定
   */
  static async disablePinning(): Promise<void> {
    try {
      const { disableSslPinning } = await import('react-native-ssl-public-key-pinning');
      await disableSslPinning();
      this.pinnedDomains.clear();
      console.log('SSL pinning disabled');
    } catch (error) {
      console.warn('Failed to disable SSL pinning:', error);
    }
  }
}

// ==================== 新增的加密和网络工具函数 ====================

/**
 * Reality密钥对接口
 */
export interface RealityKeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * TLS证书对接口
 */
export interface TLSCertificatePair {
  certificate: string;
  privateKey: string;
}

/**
 * TLS证书生成选项
 */
export interface TLSCertificateOptions {
  keySize?: number;             // RSA密钥大小，默认2048
  validityDays?: number;        // 证书有效期天数，默认365
}

/**
 * Wireguard密钥对接口
 */
export interface WireguardKeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * 使用@noble/curves的x25519生成Reality所需格式的公钥和私钥
 * Reality使用X25519密钥对，生成真正的密钥对并转换为base64url格式
 * @returns Promise<RealityKeyPair> 返回包含公钥和私钥的对象
 */
export async function generateRealityKeys(): Promise<RealityKeyPair> {
  try {
    // 使用@noble/curves的x25519生成密钥对
    const keys = x25519.keygen();

    // 转换为base64url格式（Reality协议要求）
    const publicKeyBase64 = btoa(String.fromCharCode(...keys.publicKey));
    const privateKeyBase64 = btoa(String.fromCharCode(...keys.secretKey));

    // 转换为base64url格式（替换字符并移除填充）
    const publicKey = publicKeyBase64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    const privateKey = privateKeyBase64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');

    return {
      publicKey,
      privateKey
    };
  } catch (error) {
    console.error('Failed to generate Reality keys:', error);
    throw new Error('Failed to generate Reality keys');
  }
}

/**
 * 生成TLS证书对，接受一个host参数
 * @param host 证书的主机名/域名
 * @param options 证书生成选项
 * @returns Promise<TLSCertificatePair> 返回包含证书和私钥的对象
 */
export async function generateTLSCertificate(
  host: string,
  options: TLSCertificateOptions = {}
): Promise<TLSCertificatePair> {
  try {
    const { validityDays = 365 } = options;

    // 生成一个新的密钥对
    const keys = forge.pki.ecdsa.generateKeyPair({name:"p256"});
    // 创建证书
    const cert = forge.pki.createCertificate();
    cert.publicKey = keys.publicKey
    cert.serialNumber = '01';
    cert.validity.notBefore = new Date();
    cert.validity.notAfter = new Date();
    cert.validity.notAfter.setDate(cert.validity.notBefore.getDate() + validityDays);

    // 设置证书主体
    const attrs = [
      { name: 'commonName', value: host },
      { name: 'countryName', value: 'US' },
      { shortName: 'ST', value: 'Virginia' },
      { name: 'localityName', value: 'Blacksburg' },
      { name: 'organizationName', value: 'Test' },
      { shortName: 'OU', value: 'Test' }
    ];
    cert.setSubject(attrs);
    cert.setIssuer(attrs);

    // 设置扩展
    cert.setExtensions([
      {
        name: 'basicConstraints',
        cA: false
      },
      {
        name: 'keyUsage',
        keyCertSign: true,
        digitalSignature: true,
        nonRepudiation: true,
        keyEncipherment: true,
        dataEncipherment: true
      },
      {
        name: 'extKeyUsage',
        serverAuth: true,
        clientAuth: true,
        codeSigning: true,
        emailProtection: true,
        timeStamping: true
      },
      {
        name: 'nsCertType',
        client: true,
        server: true,
        email: true,
        objsign: true,
        sslCA: true,
        emailCA: true,
        objCA: true
      }
    ]);

    // 自签名证书
    cert.sign(keys.privateKey,forge.md.sha256.create());

    // 转换为PEM格式
    const certificate = forge.pki.certificateToPem(cert);
    const privateKey = keys.privateKey.toPem()

    return {
      certificate,
      privateKey
    };
  } catch (error) {
    console.error('Failed to generate TLS certificate:', error);
    throw new Error('Failed to generate TLS certificate');
  }
}

/**
 * 生成Wireguard密钥对
 * Wireguard使用X25519 (Curve25519) 密钥对，使用@noble/curves生成实际可用的密钥对
 * @returns Promise<WireguardKeyPair> 返回包含公钥和私钥的对象
 */
export async function generateWireguardKeys(): Promise<WireguardKeyPair> {
  try {
    // 生成密钥对
    const keys = x25519.keygen();

    // 转换为标准base64格式（Wireguard使用标准base64）
    const publicKey = btoa(String.fromCharCode(...keys.publicKey));
    const privateKey = btoa(String.fromCharCode(...keys.secretKey));

    return {
      publicKey,
      privateKey
    };
  } catch (error) {
    console.error('Failed to generate Wireguard keys:', error);
    throw new Error('Failed to generate Wireguard keys');
  }
}

/**
 * 生成随机的Reality Short IDs
 * @param count 生成的Short ID数量，默认5个
 * @param length 每个Short ID的长度，默认6位
 * @returns string[] 生成的Short ID数组
 */
export function generateRandomShortIds(count: number = 5, length: number = 6): string[] {
  const shortIds: string[] = [];
  const chars = '0123456789abcdef';

  for (let i = 0; i < count; i++) {
    let shortId = '';
    for (let j = 0; j < length; j++) {
      shortId += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    shortIds.push(shortId);
  }

  return shortIds;
}

/**
 * 生成随机的base64编码密钥
 * @param bytes 密钥字节数，默认16字节
 * @returns string base64编码的密钥
 */
export function generateRandomBase64Key(bytes: number = 16): string {
  const array = new Uint8Array(bytes);

  // 生成随机字节
  for (let i = 0; i < bytes; i++) {
    array[i] = Math.floor(Math.random() * 256);
  }

  // 转换为base64
  let binary = '';
  for (let i = 0; i < array.length; i++) {
    binary += String.fromCharCode(array[i]);
  }

  return btoa(binary);
}

/**
 * 确保指定配置的认证有效
 * @param configId 配置ID
 * @returns Promise<boolean> 返回认证是否成功
 */
async function ensureAuthentication(configId: string): Promise<boolean> {
  try {
    const store = useAppStore.getState();
    const configs = store.configs;
    const config = configs.find(c => c.id === configId);

    if (!config) {
      console.error('Config not found:', configId);
      return false;
    }

    // 根据配置类型进行登录（s-ui不需要登录）
    let authInfo = null;
    switch (config.type) {
      case '3x-ui': {
        authInfo = await loginThreeXUI(config as any);
        break;
      }
      case 'x-ui': {
        authInfo = await loginXUI(config as any);
        break;
      }
    }

    if (authInfo) {
      store.setAuthInfo(configId, authInfo);
      return true;
    }

    return false;
  } catch (error) {
    console.error('Authentication failed for config:', configId, error);
    return false;
  }
}

/**
 * 智能fetch函数，支持中转服务器和自动认证
 * @param url 原始请求URL
 * @param options 原始请求选项
 * @param config 可选的配置对象，用于SSL固定和认证管理
 * @returns Promise<Response> 返回响应对象
 */
export async function smartFetch(
  url: string,
  options: RequestInit = {},
  config?: any
): Promise<Response> {
  const store = useAppStore.getState();
  const proxyServer = store.getProxyServer();

  // 如果提供了config，处理SSL固定和认证逻辑
  if (config) {
    const authInfo = store.authInfo[config.id];

    if (config.certFingerprints && config.certFingerprints.length > 0) {
      // 如果尚未固定公钥，进行SSL固定
      if (!authInfo?.isPinned) {
        const urlObj = new URL(url);
        const pinned = await SSLPinningManager.initializePinning(urlObj.hostname, config.certFingerprints);
        if (pinned && authInfo) {
          // 更新authInfo中的固定状态
          authInfo.isPinned = true;
          store.setAuthInfo(config.id, authInfo);
        }
      }
    }

    // 检查认证是否过期（仅对需要登录的面板类型）
    if (config.type !== 's-ui' && !store.isAuthValid(config.id)) {
      await ensureAuthentication(config.id);
    }
  }

  // 内部fetch函数，处理实际的网络请求
  const performFetch = async (fetchOptions: RequestInit): Promise<Response> => {
    if (proxyServer) {
      // 使用中转服务器
      const proxyUrl = `${proxyServer.url}/proxy`;
      const proxyHeaders = new Headers(fetchOptions.headers);

      // 添加中转服务器的认证token
      proxyHeaders.set('Authorization', `Bearer ${proxyServer.token}`);
      proxyHeaders.set('Content-Type', 'application/json');

      // 构建中转请求体
      const proxyBody = {
        url: url,
        method: fetchOptions.method || 'GET',
        headers: Object.fromEntries(new Headers(fetchOptions.headers || {})),
        body: fetchOptions.body
      };

      // 发送到中转服务器
      const proxyOptions: RequestInit = {
        method: 'POST',
        headers: proxyHeaders,
        body: JSON.stringify(proxyBody),
        credentials: fetchOptions.credentials,
        signal: fetchOptions.signal
      };

      const response = await fetch(proxyUrl, proxyOptions);

      // 检查中转服务器响应
      if (!response.ok) {
        throw new Error(`Proxy server error: ${response.status} ${response.statusText}`);
      }

      // 解析中转服务器的响应
      const proxyResponse = await response.json();

      // 创建模拟的Response对象
      return new Response(
        proxyResponse.body || null,
        {
          status: proxyResponse.status || 200,
          statusText: proxyResponse.statusText || 'OK',
          headers: new Headers(proxyResponse.headers || {})
        }
      );
    } else {
      // 直接发送请求
      return await fetch(url, fetchOptions);
    }
  };

  try {
    // 执行请求
    const response = await performFetch(options);

    // 如果返回401且提供了config，尝试重新认证（仅对需要登录的面板类型）
    if (response.status === 401 && config) {
      if (config.type !== 's-ui') {
        console.log('Received 401, attempting re-authentication...');
        await ensureAuthentication(config.id);

        // 重试请求
        return await performFetch(options);
      }
    }

    return response;
  } catch (error) {
    console.error('Smart fetch failed:', error);
    throw error;
  }
}
