import React from 'react';
import { View, Text, Alert } from 'react-native';
import { useSubscription } from '@/hooks/useSubscription';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Crown, ShoppingCart, RotateCcw } from '@/lib/icons';

/**
 * 订阅管理示例组件
 * 
 * 展示如何使用 useSubscription hook 进行：
 * - 显示订阅状态
 * - 购买Pro订阅
 * - 恢复购买
 * - 错误处理
 * - 加载状态管理
 */
export function SubscriptionExample() {
  const {
    isPro,
    isLoading,
    isConnected,
    purchasePro,
    restorePurchases,
    error,
    products,
    subscriptions,
    currentPurchase,
  } = useSubscription();

  const handlePurchase = async () => {
    try {
      await purchasePro();
      Alert.alert('Success', 'Subscription purchased successfully!');
    } catch (error) {
      console.error('Purchase failed:', error);
      // 错误已经在hook中处理，这里可以显示额外的UI反馈
    }
  };

  const handleRestore = async () => {
    try {
      await restorePurchases();
      Alert.alert('Success', 'Purchases restored successfully!');
    } catch (error) {
      console.error('Restore failed:', error);
      // 错误已经在hook中处理
    }
  };

  return (
    <View className="p-4 space-y-4">
      {/* 连接状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-row items-center gap-2">
            <Crown className="h-5 w-5" />
            Subscription Status
          </CardTitle>
          <CardDescription>
            Manage your Pro subscription
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 状态指示器 */}
          <View className="flex flex-row items-center justify-between">
            <Text className="text-base">Store Connection:</Text>
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? "Connected" : "Disconnected"}
            </Badge>
          </View>

          <View className="flex flex-row items-center justify-between">
            <Text className="text-base">Pro Status:</Text>
            <Badge variant={isPro ? "default" : "secondary"}>
              {isPro ? "Active" : "Inactive"}
            </Badge>
          </View>

          {/* 错误显示 */}
          {error && (
            <View className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <Text className="text-red-600 dark:text-red-400 text-sm">
                {error}
              </Text>
            </View>
          )}

          {/* 当前购买信息 */}
          {currentPurchase && (
            <View className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Text className="text-green-600 dark:text-green-400 text-sm">
                Purchase in progress: {currentPurchase.id}
              </Text>
            </View>
          )}
        </CardContent>
      </Card>

      {/* 产品信息 */}
      {subscriptions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Available Subscriptions</CardTitle>
          </CardHeader>
          <CardContent>
            {subscriptions.map((subscription) => (
              <View key={subscription.id} className="p-3 border rounded-lg mb-2">
                <Text className="font-semibold">{subscription.title}</Text>
                <Text className="text-sm text-gray-600 dark:text-gray-400">
                  {subscription.description}
                </Text>
                <Text className="text-lg font-bold text-blue-600">
                  {subscription.displayPrice}
                </Text>
              </View>
            ))}
          </CardContent>
        </Card>
      )}

      {/* 操作按钮 */}
      <Card>
        <CardContent className="pt-6 space-y-3">
          {!isPro && (
            <Button
              onPress={handlePurchase}
              disabled={isLoading || !isConnected}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Purchase Pro Subscription
                </>
              )}
            </Button>
          )}

          <Button
            variant="outline"
            onPress={handleRestore}
            disabled={isLoading || !isConnected}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Restoring...
              </>
            ) : (
              <>
                <RotateCcw className="mr-2 h-4 w-4" />
                Restore Purchases
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* 调试信息 */}
      {__DEV__ && (
        <Card>
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-xs font-mono">
              Products: {products.length}
            </Text>
            <Text className="text-xs font-mono">
              Subscriptions: {subscriptions.length}
            </Text>
            <Text className="text-xs font-mono">
              Connected: {isConnected.toString()}
            </Text>
            <Text className="text-xs font-mono">
              Loading: {isLoading.toString()}
            </Text>
            <Text className="text-xs font-mono">
              Error: {error || 'None'}
            </Text>
          </CardContent>
        </Card>
      )}
    </View>
  );
}
