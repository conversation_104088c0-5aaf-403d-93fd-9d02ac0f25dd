# Expo IAP 集成指南

本项目已经实现了设置页面的基础结构，包括订阅管理功能。当前使用的是模拟实现，以下是集成真实 expo-iap 的步骤。

## 1. 安装依赖

```bash
npx expo install expo-iap
```

## 2. 配置 app.json

由于 expo-iap 使用 Google Play Billing Library v8.0.0，需要 Kotlin 2.0+：

```json
{
  "expo": {
    "plugins": [
      [
        "expo-build-properties",
        {
          "android": {
            "kotlinVersion": "2.0.21"
          }
        }
      ]
    ]
  }
}
```

## 3. 更新 useSubscription Hook

替换 `hooks/useSubscription.ts` 中的模拟实现：

```typescript
import { useIAP } from 'expo-iap';

export const useSubscription = (): SubscriptionHook => {
  const { settings, updateSettings } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    connected,
    products,
    subscriptions,
    getProducts,
    getSubscriptions,
    requestPurchase,
    restorePurchases: iapRestorePurchases,
    currentPurchase,
    finishTransaction,
    currentPurchaseError,
  } = useIAP();

  // 实现真实的IAP逻辑...
};
```

## 4. 配置应用商店

### iOS (App Store Connect)
1. 登录 App Store Connect
2. 选择你的应用
3. 进入 "功能" > "App 内购买项目"
4. 创建新的订阅产品
5. 设置产品ID为 `pro_subscription`（或更新代码中的 PRODUCT_ID）

### Android (Google Play Console)
1. 登录 Google Play Console
2. 选择你的应用
3. 进入 "货币化" > "产品" > "订阅"
4. 创建新的订阅产品
5. 设置产品ID为 `pro_subscription`

## 5. 测试

### iOS 测试
- 使用 Xcode 的 StoreKit Configuration 文件进行本地测试
- 使用 TestFlight 进行沙盒测试

### Android 测试
- 使用 Google Play Console 的内部测试轨道
- 确保测试账户已添加到许可测试人员列表

## 6. 最佳实践

### 错误处理
```typescript
try {
  await purchasePro();
} catch (error) {
  if (error.code === 'UserCancel') {
    // 用户取消购买
  } else if (error.code === 'PaymentNotAllowed') {
    // 支付不被允许
  } else {
    // 其他错误
  }
}
```

### 购买验证
在生产环境中，应该在服务器端验证购买收据：

```typescript
const verifyPurchase = async (purchase) => {
  const response = await fetch('/api/verify-purchase', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ purchase }),
  });
  
  return response.json();
};
```

### 状态持久化
确保订阅状态正确保存到本地存储：

```typescript
useEffect(() => {
  if (currentPurchase) {
    // 验证购买后保存状态
    updateSettings({ isPro: true });
    SecureStore.setItemAsync('isPro', 'true');
  }
}, [currentPurchase]);
```

## 7. 注意事项

1. **测试环境**：始终在沙盒环境中测试，避免真实扣费
2. **收据验证**：生产环境必须验证购买收据
3. **错误处理**：妥善处理网络错误、用户取消等情况
4. **用户体验**：提供清晰的购买流程和状态反馈
5. **合规性**：遵守应用商店的订阅政策

## 8. 相关文档

- [Expo IAP 官方文档](https://expo-iap.hyo.dev/)
- [Apple App Store 订阅指南](https://developer.apple.com/app-store/subscriptions/)
- [Google Play 订阅指南](https://developer.android.com/google/play/billing/subscriptions)
